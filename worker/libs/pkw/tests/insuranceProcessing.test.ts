import { test, describe } from 'node:test';
import assert from 'node:assert';
import {
    calculateInsuranceAmount,
    calculateCurrentOdds,
    shouldBuyInsurance,
    getCoverageAmountWeights,
    SERVER_COVERAGE_AMOUNT,
    LOW_OUTS_COUNT,
    MEDIUM_OUTS_COUNT,
    HIGH_OUTS_COUNT,
} from '../src/pkw_ts/tools/insuranceProcessing';
import { protocol } from '../src/proto/gs_protocol';

function createMockInsuranceNotice(
    overrides: Partial<protocol.INoticeGameInsurance> = {},
): protocol.INoticeGameInsurance {
    return {
        outs: [],
        leftCards: 47,
        buy_amount: 0,
        total_inv_amount: 1000,
        pot_amount: 500,
        limit_amount: 100,
        public_cards: [],
        roomid: 1,
        player_seats: [],
        buyer_seatid: 1,
        buyer_uid: 1,
        total_pot_amount: 500,
        action_seq: 1,
        inv_amount: 100,
        total_pot: 500,
        count_time: 30,
        force_amount: 0,
        foldCards: [],
        error: 0,
        NoOutsTimeOut: 10,
        ...overrides,
    };
}

describe('Insurance Processing', () => {
    describe('Constants', () => {
        test('should have correct constant values', () => {
            assert.equal(LOW_OUTS_COUNT, 3);
            assert.equal(MEDIUM_OUTS_COUNT, 7);
            assert.equal(HIGH_OUTS_COUNT, 12);
        });

        test('SERVER_COVERAGE_AMOUNT should have correct values', () => {
            assert.equal(SERVER_COVERAGE_AMOUNT.LOW, 1 / 8);
            assert.equal(SERVER_COVERAGE_AMOUNT.MEDIUM_LOW, 1 / 5);
            assert.equal(SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH, 1 / 3);
            assert.equal(SERVER_COVERAGE_AMOUNT.BREAK_EAVEN, 1 / 2);
            assert.equal(SERVER_COVERAGE_AMOUNT.FULL_POT, 1);
        });
    });

    describe('calculateCurrentOdds', () => {
        test('should calculate odds correctly for normal cases', () => {
            // Test case: 47 remaining cards, 9 outs
            // Expected: (47 / 9) * 0.95 - 1 = 4.97 - 1 = 3.97, rounded to 4.0
            const result = calculateCurrentOdds(47, 9);
            assert.equal(result, 4.0);
        });

        test('should calculate odds correctly for different scenarios', () => {
            // Test case: 46 remaining cards, 8 outs
            // Expected: (46 / 8) * 0.95 - 1 = 5.475 - 1 = 4.475, rounded to 4.5
            const result = calculateCurrentOdds(46, 8);
            assert.equal(result, 4.5);
        });

        test('should return 0 for very low odds (< 0.1)', () => {
            const result = calculateCurrentOdds(5, 20);
            assert.equal(result, 0);
        });

        test('should handle edge case with 1 out', () => {
            const result = calculateCurrentOdds(47, 1);
            assert.equal(result, 43.7);
        });

        test('should handle zero outs (division by zero)', () => {
            const result = calculateCurrentOdds(47, 0);
            assert.equal(result, 0);
        });

        test('should handle negative remaining cards', () => {
            const result = calculateCurrentOdds(-5, 9);
            assert.equal(result, 0);
        });

        test('should handle negative outs', () => {
            const result = calculateCurrentOdds(47, -5);
            assert.equal(result, 0);
        });
    });

    describe('shouldBuyInsurance', () => {
        let originalRandom: () => number;

        test.beforeEach(() => {
            originalRandom = Math.random;
        });

        test.afterEach(() => {
            Math.random = originalRandom;
        });

        test('should return false for low outs count when random is high', () => {
            Math.random = () => 0.9; // Higher than 0.1 probability
            const result = shouldBuyInsurance(2); // LOW_OUTS_COUNT = 3
            assert.equal(result, false);
        });

        test('should return true for low outs count when random is low', () => {
            Math.random = () => 0.05; // Lower than 0.1 probability
            const result = shouldBuyInsurance(3); // LOW_OUTS_COUNT = 3
            assert.equal(result, true);
        });

        test('should return false for medium outs count when random is high', () => {
            Math.random = () => 0.9; // Higher than 0.25 probability
            const result = shouldBuyInsurance(5); // Between LOW and MEDIUM
            assert.equal(result, false);
        });

        test('should return true for medium outs count when random is low', () => {
            Math.random = () => 0.2; // Lower than 0.25 probability
            const result = shouldBuyInsurance(7); // MEDIUM_OUTS_COUNT = 7
            assert.equal(result, true);
        });

        test('should return false for high outs count when random is high', () => {
            Math.random = () => 0.9; // Higher than 0.35 probability
            const result = shouldBuyInsurance(10); // Between MEDIUM and HIGH
            assert.equal(result, false);
        });

        test('should return true for high outs count when random is low', () => {
            Math.random = () => 0.3; // Lower than 0.35 probability
            const result = shouldBuyInsurance(12); // HIGH_OUTS_COUNT = 12
            assert.equal(result, true);
        });

        test('should return false for very high outs count when random is high', () => {
            Math.random = () => 0.9; // Higher than 0.55 probability
            const result = shouldBuyInsurance(15); // Higher than HIGH_OUTS_COUNT
            assert.equal(result, false);
        });

        test('should return true for very high outs count when random is low', () => {
            Math.random = () => 0.5; // Lower than 0.55 probability
            const result = shouldBuyInsurance(20); // Much higher than HIGH_OUTS_COUNT
            assert.equal(result, true);
        });

        test('should handle zero outs', () => {
            Math.random = () => 0.05; // Very low random
            const result = shouldBuyInsurance(0);
            assert.equal(result, true); // Should use 0.1 probability for <= LOW_OUTS_COUNT
        });

        test('should handle negative outs', () => {
            Math.random = () => 0.05; // Very low random
            const result = shouldBuyInsurance(-5);
            assert.equal(result, true); // Should use 0.1 probability for <= LOW_OUTS_COUNT
        });
    });

    describe('getCoverageAmountWeights', () => {
        test('should return correct weights for low outs count', () => {
            const result = getCoverageAmountWeights(2);

            assert.equal(result.length, 3);
            assert.equal(result[0].covarageAmount, SERVER_COVERAGE_AMOUNT.LOW);
            assert.equal(result[0].weight, 50);
            assert.equal(result[1].covarageAmount, SERVER_COVERAGE_AMOUNT.MEDIUM_LOW);
            assert.equal(result[1].weight, 35);
            assert.equal(result[2].covarageAmount, SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH);
            assert.equal(result[2].weight, 15);
        });

        test('should return correct weights for exactly low outs count', () => {
            const result = getCoverageAmountWeights(3); // Exactly LOW_OUTS_COUNT

            assert.equal(result.length, 3);
            assert.equal(result[0].covarageAmount, SERVER_COVERAGE_AMOUNT.LOW);
            assert.equal(result[0].weight, 50);
        });

        test('should return correct weights for medium outs count', () => {
            const result = getCoverageAmountWeights(5); // Between LOW and MEDIUM

            assert.equal(result.length, 4);
            assert.equal(result[0].covarageAmount, SERVER_COVERAGE_AMOUNT.LOW);
            assert.equal(result[0].weight, 30);
            assert.equal(result[1].covarageAmount, SERVER_COVERAGE_AMOUNT.MEDIUM_LOW);
            assert.equal(result[1].weight, 40);
            assert.equal(result[2].covarageAmount, SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH);
            assert.equal(result[2].weight, 20);
            assert.equal(result[3].covarageAmount, SERVER_COVERAGE_AMOUNT.BREAK_EAVEN);
            assert.equal(result[3].weight, 10);
        });

        test('should return correct weights for exactly medium outs count', () => {
            const result = getCoverageAmountWeights(7); // Exactly MEDIUM_OUTS_COUNT

            assert.equal(result.length, 4);
            assert.equal(result[1].covarageAmount, SERVER_COVERAGE_AMOUNT.MEDIUM_LOW);
            assert.equal(result[1].weight, 40);
        });

        test('should return correct weights for high outs count', () => {
            const result = getCoverageAmountWeights(10); // Between MEDIUM and HIGH

            assert.equal(result.length, 4);
            assert.equal(result[0].covarageAmount, SERVER_COVERAGE_AMOUNT.MEDIUM_LOW);
            assert.equal(result[0].weight, 25);
            assert.equal(result[1].covarageAmount, SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH);
            assert.equal(result[1].weight, 35);
            assert.equal(result[2].covarageAmount, SERVER_COVERAGE_AMOUNT.BREAK_EAVEN);
            assert.equal(result[2].weight, 30);
            assert.equal(result[3].covarageAmount, SERVER_COVERAGE_AMOUNT.FULL_POT);
            assert.equal(result[3].weight, 10);
        });

        test('should return correct weights for exactly high outs count', () => {
            const result = getCoverageAmountWeights(12); // Exactly HIGH_OUTS_COUNT

            assert.equal(result.length, 4);
            assert.equal(result[2].covarageAmount, SERVER_COVERAGE_AMOUNT.BREAK_EAVEN);
            assert.equal(result[2].weight, 30);
        });

        test('should return correct weights for very high outs count', () => {
            const result = getCoverageAmountWeights(15); // > HIGH_OUTS_COUNT

            assert.equal(result.length, 3);
            assert.equal(result[0].covarageAmount, SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH);
            assert.equal(result[0].weight, 20);
            assert.equal(result[1].covarageAmount, SERVER_COVERAGE_AMOUNT.BREAK_EAVEN);
            assert.equal(result[1].weight, 40);
            assert.equal(result[2].covarageAmount, SERVER_COVERAGE_AMOUNT.FULL_POT);
            assert.equal(result[2].weight, 40);
        });

        test('should handle zero outs', () => {
            const result = getCoverageAmountWeights(0);

            // Should use low outs logic (0 <= LOW_OUTS_COUNT)
            assert.equal(result.length, 3);
            assert.equal(result[0].weight, 50);
        });

        test('should handle negative outs', () => {
            const result = getCoverageAmountWeights(-5);

            // Should use low outs logic (negative <= LOW_OUTS_COUNT)
            assert.equal(result.length, 3);
            assert.equal(result[0].weight, 50);
        });

        test('should verify all weights sum correctly for each category', () => {
            const lowResult = getCoverageAmountWeights(2);
            const lowSum = lowResult.reduce((sum, item) => sum + item.weight, 0);
            assert.equal(lowSum, 100);

            const mediumResult = getCoverageAmountWeights(5);
            const mediumSum = mediumResult.reduce((sum, item) => sum + item.weight, 0);
            assert.equal(mediumSum, 100);

            const highResult = getCoverageAmountWeights(10);
            const highSum = highResult.reduce((sum, item) => sum + item.weight, 0);
            assert.equal(highSum, 100);

            const veryHighResult = getCoverageAmountWeights(15);
            const veryHighSum = veryHighResult.reduce((sum, item) => sum + item.weight, 0);
            assert.equal(veryHighSum, 100);
        });
    });

    describe('calculateInsuranceAmount', () => {
        let originalRandom: () => number;

        test.beforeEach(() => {
            originalRandom = Math.random;
        });

        test.afterEach(() => {
            Math.random = originalRandom;
        });

        test('should return 0 when shouldBuyInsurance returns false and insurance not required', () => {
            Math.random = () => 0.9; // High random to make shouldBuyInsurance return false

            const msg = createMockInsuranceNotice({
                outs: [{ outs_id: 1 }, { outs_id: 2 }], // 2 outs (low)
                buy_amount: 0, // No previous insurance bought
            });

            const result = calculateInsuranceAmount(msg);
            assert.equal(result, 0);
        });

        test('should return 0 when currentOdds is 0', () => {
            Math.random = () => 0.05; // Low random to make shouldBuyInsurance return true

            const msg = createMockInsuranceNotice({
                outs: Array(20)
                    .fill(0)
                    .map((_, i) => ({ outs_id: i + 1 })), // 20 outs
                leftCards: 5, // Very few cards left, will result in low odds < 0.1
                buy_amount: 0,
            });

            const result = calculateInsuranceAmount(msg);
            assert.equal(result, 0);
        });

        test('should return 0 when insurance is required but odds are 0', () => {
            Math.random = () => 0.9; // High random, but insurance is required

            const msg = createMockInsuranceNotice({
                outs: Array(20)
                    .fill(0)
                    .map((_, i) => ({ outs_id: i + 1 })), // 20 outs
                buy_amount: 100, // Previous insurance bought - makes it required
                leftCards: 5, // Very few cards left, will result in low odds < 0.1
            });

            const result = calculateInsuranceAmount(msg);
            assert.equal(result, 0);
        });

        test('should handle edge case with empty outs array', () => {
            const msg = createMockInsuranceNotice({
                outs: [],
                buy_amount: 0,
            });

            const result = calculateInsuranceAmount(msg);
            assert.equal(result, 0);
        });

        test('should handle insurance required scenario', () => {
            Math.random = () => 0.9; // High random, but insurance is required

            const msg = createMockInsuranceNotice({
                outs: [{ outs_id: 1 }, { outs_id: 2 }], // 2 outs
                buy_amount: 100, // Previous insurance bought - makes it required
                leftCards: 47,
                pot_amount: 1000,
                limit_amount: 50,
            });

            const result = calculateInsuranceAmount(msg);
            // Should calculate some insurance amount since it's required
            // The exact amount depends on the weightedRandomSelection result
            assert.ok(typeof result === 'number');
        });

        test('should handle edge case with empty outs array', () => {
            const msg = createMockInsuranceNotice({
                outs: [],
                buy_amount: 0,
            });

            const result = calculateInsuranceAmount(msg);
            assert.equal(result, 0);
        });
    });

    describe('Integration Tests', () => {
        test('should handle basic integration scenario', () => {
            Math.random = () => 0.2; // Will trigger shouldBuyInsurance for medium outs

            const msg = createMockInsuranceNotice({
                outs: Array(9)
                    .fill(0)
                    .map((_, i) => ({ outs_id: i + 1 })), // 9 outs (flush draw)
                leftCards: 47,
                pot_amount: 1200,
                limit_amount: 60,
                buy_amount: 0,
                total_inv_amount: 800,
                public_cards: [
                    { number: 1, suit: 1 },
                    { number: 2, suit: 2 },
                    { number: 3, suit: 3 },
                ], // Flop
            });

            const result = calculateInsuranceAmount(msg);

            // Should return a number (either 0 or positive integer)
            assert.ok(typeof result === 'number');
            assert.ok(Number.isInteger(result));
            assert.ok(result >= 0);
        });
    });
});
